import os
import logging
import json
import time
import base64
from datetime import datetime, timedelta
from functools import wraps

import requests
import redis
import psycopg2
from flask import Flask, request, jsonify, Response, stream_with_context
from flask_cors import CORS
from werkzeug.utils import secure_filename

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configure CORS to allow requests from frontend
# Get allowed origins from environment or use defaults
allowed_origins = os.getenv('CORS_ORIGINS', 'http://localhost:86,http://127.0.0.1:86').split(',')
# Strip whitespace from origins
allowed_origins = [origin.strip() for origin in allowed_origins]
logger.info(f"CORS allowed origins: {allowed_origins}")

CORS(app, origins=allowed_origins,
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allow_headers=['Content-Type', 'X-Client-ID', 'X-Client-Secret', 'Authorization', 'X-Requested-With'],
     supports_credentials=True)

# Configuration
OLLAMA_URL = os.getenv('OLLAMA_URL', 'http://host.docker.internal:11434')
AUTH_SERVICE_URL = 'http://ollama-auth-service:8001'

# Database configuration
DB_CONFIG = {
    'host': os.getenv('POSTGRES_HOST', 'localhost'),
    'port': os.getenv('POSTGRES_PORT', 5432),
    'database': os.getenv('POSTGRES_DB', 'calcounta_gateway'),
    'user': os.getenv('POSTGRES_USER', 'gateway_user'),
    'password': os.getenv('POSTGRES_PASSWORD', 'password')
}

# Redis configuration
REDIS_CONFIG = {
    'host': os.getenv('REDIS_HOST', 'localhost'),
    'port': int(os.getenv('REDIS_PORT', 6379)),
    'password': os.getenv('REDIS_PASSWORD', None),
    'decode_responses': True
}

# Rate limiting configuration
RATE_LIMITS = {
    'per_minute': int(os.getenv('RATE_LIMIT_PER_MINUTE', 60)),
    'per_hour': int(os.getenv('RATE_LIMIT_PER_HOUR', 1000)),
    'per_day': int(os.getenv('RATE_LIMIT_PER_DAY', 10000))
}

# Initialize Redis connection
try:
    redis_client = redis.Redis(**REDIS_CONFIG)
    redis_client.ping()
    logger.info("Redis connection established")
except Exception as e:
    logger.error(f"Redis connection failed: {e}")
    redis_client = None

# Database connection helper
def get_db_connection():
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise

# Authentication and rate limiting decorator
def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get credentials from headers
        client_id = request.headers.get('X-Client-ID')
        client_secret = request.headers.get('X-Client-Secret')
        
        if not client_id or not client_secret:
            return jsonify({
                'error': 'Missing credentials',
                'message': 'X-Client-ID and X-Client-Secret headers are required'
            }), 401
        
        # Validate credentials with auth service
        try:
            auth_response = requests.post(
                f"{AUTH_SERVICE_URL}/api/keys/validate",
                json={'client_id': client_id, 'client_secret': client_secret},
                timeout=5
            )
            
            if auth_response.status_code != 200:
                auth_data = auth_response.json()
                return jsonify({
                    'error': 'Invalid credentials',
                    'message': auth_data.get('error', 'Authentication failed')
                }), 401
            
            auth_data = auth_response.json()
            if not auth_data.get('valid'):
                return jsonify({
                    'error': 'Invalid credentials',
                    'message': auth_data.get('error', 'Authentication failed')
                }), 401
            
        except requests.RequestException as e:
            logger.error(f"Auth service error: {e}")
            return jsonify({
                'error': 'Authentication service unavailable',
                'message': 'Please try again later'
            }), 503
        
        # Check rate limits
        api_key_id = auth_data['api_key_id']
        rate_limits = auth_data['rate_limits']
        
        if not check_rate_limits(api_key_id, rate_limits):
            return jsonify({
                'error': 'Rate limit exceeded',
                'message': 'Too many requests. Please slow down.'
            }), 429
        
        # Store auth data in request context
        request.auth_data = auth_data
        
        return f(*args, **kwargs)
    
    return decorated_function

def check_rate_limits(api_key_id, rate_limits):
    """Check if request is within rate limits"""
    if not redis_client:
        return True  # Allow if Redis is not available
    
    now = datetime.utcnow()
    
    # Check minute limit
    minute_key = f"rate_limit:{api_key_id}:minute:{now.strftime('%Y%m%d%H%M')}"
    minute_count = redis_client.incr(minute_key)
    if minute_count == 1:
        redis_client.expire(minute_key, 60)
    if minute_count > rate_limits['per_minute']:
        return False
    
    # Check hour limit
    hour_key = f"rate_limit:{api_key_id}:hour:{now.strftime('%Y%m%d%H')}"
    hour_count = redis_client.incr(hour_key)
    if hour_count == 1:
        redis_client.expire(hour_key, 3600)
    if hour_count > rate_limits['per_hour']:
        return False
    
    # Check day limit
    day_key = f"rate_limit:{api_key_id}:day:{now.strftime('%Y%m%d')}"
    day_count = redis_client.incr(day_key)
    if day_count == 1:
        redis_client.expire(day_key, 86400)
    if day_count > rate_limits['per_day']:
        return False
    
    return True

def log_api_usage(endpoint, method, status_code, response_time_ms, 
                  request_size=None, response_size=None, error_message=None):
    """Log API usage to database"""
    try:
        auth_data = getattr(request, 'auth_data', None)
        if not auth_data:
            return
        
        conn = get_db_connection()
        cur = conn.cursor()
        
        cur.execute("""
            INSERT INTO ollama_api_usage_logs (
                api_key_id, client_id, endpoint, method, status_code,
                response_time_ms, request_size_bytes, response_size_bytes,
                ip_address, user_agent, error_message
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            auth_data['api_key_id'],
            request.headers.get('X-Client-ID'),
            endpoint,
            method,
            status_code,
            response_time_ms,
            request_size,
            response_size,
            request.remote_addr,
            request.headers.get('User-Agent'),
            error_message
        ))
        
        conn.commit()
        cur.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Failed to log API usage: {e}")

# CORS preflight handler for all AI API routes
@app.route('/ai/api/<path:endpoint>', methods=['OPTIONS'])
def handle_preflight(endpoint):
    """Handle CORS preflight requests"""
    return '', 200

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Check Ollama connection
        ollama_response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        ollama_status = "connected" if ollama_response.status_code == 200 else "disconnected"
        
        # Check database connection
        conn = get_db_connection()
        conn.close()
        
        # Check Redis connection
        redis_status = "connected" if redis_client and redis_client.ping() else "disconnected"
        
        return jsonify({
            'status': 'healthy',
            'service': 'ollama-proxy',
            'ollama': ollama_status,
            'database': 'connected',
            'redis': redis_status,
            'timestamp': datetime.utcnow().isoformat()
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503

# Ollama API proxy endpoints
@app.route('/ai/api/tags', methods=['OPTIONS'])
def tags_preflight():
    """Handle CORS preflight for tags endpoint"""
    return '', 200

@app.route('/ai/api/tags', methods=['GET'])
@require_api_key
def list_models():
    """List available models"""
    start_time = time.time()

    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=30)
        response_time_ms = int((time.time() - start_time) * 1000)

        # Log usage
        log_api_usage(
            endpoint='/ai/api/tags',
            method='GET',
            status_code=response.status_code,
            response_time_ms=response_time_ms,
            response_size=len(response.content) if response.content else 0
        )

        return Response(
            response.content,
            status=response.status_code,
            headers=dict(response.headers)
        )

    except requests.RequestException as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = f"Ollama connection error: {str(e)}"

        log_api_usage(
            endpoint='/ai/api/tags',
            method='GET',
            status_code=503,
            response_time_ms=response_time_ms,
            error_message=error_msg
        )

        return jsonify({
            'error': 'Service unavailable',
            'message': 'Ollama service is not available'
        }), 503

@app.route('/ai/api/chat', methods=['POST'])
@require_api_key
def chat():
    """Chat with models"""
    start_time = time.time()

    try:
        # Get request data
        request_data = request.get_json()
        request_size = len(request.data) if request.data else 0

        # Forward request to Ollama
        response = requests.post(
            f"{OLLAMA_URL}/api/chat",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            stream=request_data.get('stream', False),
            timeout=300
        )

        response_time_ms = int((time.time() - start_time) * 1000)

        # Handle streaming response
        if request_data.get('stream', False):
            def generate():
                total_size = 0
                try:
                    for chunk in response.iter_content(chunk_size=1024):
                        if chunk:
                            total_size += len(chunk)
                            yield chunk
                finally:
                    # Log after streaming completes
                    log_api_usage(
                        endpoint='/ai/api/chat',
                        method='POST',
                        status_code=response.status_code,
                        response_time_ms=response_time_ms,
                        request_size=request_size,
                        response_size=total_size
                    )

            return Response(
                stream_with_context(generate()),
                status=response.status_code,
                headers=dict(response.headers)
            )
        else:
            # Non-streaming response
            log_api_usage(
                endpoint='/ai/api/chat',
                method='POST',
                status_code=response.status_code,
                response_time_ms=response_time_ms,
                request_size=request_size,
                response_size=len(response.content) if response.content else 0
            )

            return Response(
                response.content,
                status=response.status_code,
                headers=dict(response.headers)
            )

    except requests.RequestException as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = f"Ollama connection error: {str(e)}"

        log_api_usage(
            endpoint='/ai/api/chat',
            method='POST',
            status_code=503,
            response_time_ms=response_time_ms,
            request_size=len(request.data) if request.data else 0,
            error_message=error_msg
        )

        return jsonify({
            'error': 'Service unavailable',
            'message': 'Ollama service is not available'
        }), 503

@app.route('/ai/api/generate', methods=['POST'])
@require_api_key
def generate():
    """Generate completions"""
    start_time = time.time()

    try:
        # Get request data
        request_data = request.get_json()
        request_size = len(request.data) if request.data else 0

        # Forward request to Ollama
        response = requests.post(
            f"{OLLAMA_URL}/api/generate",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            stream=request_data.get('stream', False),
            timeout=300
        )

        response_time_ms = int((time.time() - start_time) * 1000)

        # Handle streaming response
        if request_data.get('stream', False):
            def generate():
                total_size = 0
                try:
                    for chunk in response.iter_content(chunk_size=1024):
                        if chunk:
                            total_size += len(chunk)
                            yield chunk
                finally:
                    # Log after streaming completes
                    log_api_usage(
                        endpoint='/ai/api/generate',
                        method='POST',
                        status_code=response.status_code,
                        response_time_ms=response_time_ms,
                        request_size=request_size,
                        response_size=total_size
                    )

            return Response(
                stream_with_context(generate()),
                status=response.status_code,
                headers=dict(response.headers)
            )
        else:
            # Non-streaming response
            log_api_usage(
                endpoint='/ai/api/generate',
                method='POST',
                status_code=response.status_code,
                response_time_ms=response_time_ms,
                request_size=request_size,
                response_size=len(response.content) if response.content else 0
            )

            return Response(
                response.content,
                status=response.status_code,
                headers=dict(response.headers)
            )

    except requests.RequestException as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = f"Ollama connection error: {str(e)}"

        log_api_usage(
            endpoint='/ai/api/generate',
            method='POST',
            status_code=503,
            response_time_ms=response_time_ms,
            request_size=len(request.data) if request.data else 0,
            error_message=error_msg
        )

        return jsonify({
            'error': 'Service unavailable',
            'message': 'Ollama service is not available'
        }), 503

@app.route('/ai/api/embeddings', methods=['POST'])
@require_api_key
def embeddings():
    """Generate embeddings"""
    start_time = time.time()

    try:
        # Get request data
        request_data = request.get_json()
        request_size = len(request.data) if request.data else 0

        # Forward request to Ollama
        response = requests.post(
            f"{OLLAMA_URL}/api/embeddings",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=300
        )

        response_time_ms = int((time.time() - start_time) * 1000)

        # Log usage
        log_api_usage(
            endpoint='/ai/api/embeddings',
            method='POST',
            status_code=response.status_code,
            response_time_ms=response_time_ms,
            request_size=request_size,
            response_size=len(response.content) if response.content else 0
        )

        return Response(
            response.content,
            status=response.status_code,
            headers=dict(response.headers)
        )

    except requests.RequestException as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = f"Ollama connection error: {str(e)}"

        log_api_usage(
            endpoint='/ai/api/embeddings',
            method='POST',
            status_code=503,
            response_time_ms=response_time_ms,
            request_size=len(request.data) if request.data else 0,
            error_message=error_msg
        )

        return jsonify({
            'error': 'Service unavailable',
            'message': 'Ollama service is not available'
        }), 503

# Generic proxy for other Ollama endpoints
@app.route('/ai/api/<path:endpoint>', methods=['GET', 'POST', 'PUT', 'DELETE'])
@require_api_key
def proxy_ollama(endpoint):
    """Generic proxy for other Ollama endpoints"""
    start_time = time.time()

    try:
        # Prepare request
        url = f"{OLLAMA_URL}/api/{endpoint}"
        method = request.method
        headers = {'Content-Type': 'application/json'} if request.is_json else {}

        # Get request data
        data = request.get_json() if request.is_json else None
        request_size = len(request.data) if request.data else 0

        # Forward request to Ollama
        response = requests.request(
            method=method,
            url=url,
            json=data,
            headers=headers,
            params=request.args,
            timeout=300
        )

        response_time_ms = int((time.time() - start_time) * 1000)

        # Log usage
        log_api_usage(
            endpoint=f'/ai/api/{endpoint}',
            method=method,
            status_code=response.status_code,
            response_time_ms=response_time_ms,
            request_size=request_size,
            response_size=len(response.content) if response.content else 0
        )

        return Response(
            response.content,
            status=response.status_code,
            headers=dict(response.headers)
        )

    except requests.RequestException as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = f"Ollama connection error: {str(e)}"

        log_api_usage(
            endpoint=f'/ai/api/{endpoint}',
            method=request.method,
            status_code=503,
            response_time_ms=response_time_ms,
            request_size=len(request.data) if request.data else 0,
            error_message=error_msg
        )

        return jsonify({
            'error': 'Service unavailable',
            'message': 'Ollama service is not available'
        }), 503

# Nutrition analysis schema
NUTRITION_SCHEMA = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "calories": {"type": "number"},
        "protein": {"type": "number"},
        "fat": {"type": "number"},
        "carbs": {"type": "number"},
        "fiber": {"type": "number"},
        "sugar": {"type": "number"},
        "sodium": {"type": "number"},
        "cholesterol": {"type": "number"},
        "saturated_fat": {"type": "number"},
        "trans_fat": {"type": "number"},
        "vitamin_c": {"type": "number"},
        "calcium": {"type": "number"},
        "iron": {"type": "number"},
        "potassium": {"type": "number"},
        "vitamin_d": {"type": "number"},
        "serving_size": {"type": "string"},
        "items": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "calories": {"type": "number"},
                    "portion": {"type": "string"},
                },
                "required": ["name", "calories", "portion"]
            }
        }
    },
    "required": [
        "name", "calories", "protein", "fat", "carbs", "fiber", "sugar", "sodium",
        "cholesterol", "saturated_fat", "trans_fat", "vitamin_c", "calcium", "iron",
        "potassium", "vitamin_d", "serving_size", "items"
    ]
}

# Image analysis endpoint
@app.route('/ai/api/image/', methods=['POST'])
@require_api_key
def analyze_image():
    """Analyze food images and return structured nutritional information"""
    start_time = time.time()

    try:
        # Check if request contains multipart form data (file upload)
        if 'image' in request.files:
            # Handle file upload
            image_file = request.files['image']
            model_name = request.form.get('model', 'gemma3:4b-it-qat')

            if image_file.filename == '':
                return jsonify({
                    'error': 'No file selected',
                    'message': 'Please select an image file'
                }), 400

            # Read and encode image
            image_data = image_file.read()
            image_b64 = base64.b64encode(image_data).decode()

        elif request.is_json:
            # Handle JSON request with base64 image
            request_data = request.get_json()
            image_b64 = request_data.get('image')
            model_name = request_data.get('model', 'gemma3:4b-it-qat')

            if not image_b64:
                return jsonify({
                    'error': 'Missing image data',
                    'message': 'Please provide image data in base64 format'
                }), 400
        else:
            return jsonify({
                'error': 'Invalid request format',
                'message': 'Please send either multipart form data or JSON with base64 image'
            }), 400

        request_size = len(request.data) if request.data else 0

        # Prepare payload for Ollama with detailed nutrition analysis prompt
        nutrition_prompt = """Analyze this food image and provide detailed nutritional information in the exact JSON format requested.

Look at the image and identify all food items visible. For each item, estimate the portion size and calculate nutritional values. Provide the total nutritional information for the entire meal/serving.

Return ONLY a JSON object with these exact fields:
- name: Overall name/description of the food/meal
- calories: Total calories (number)
- protein: Protein in grams (number)
- fat: Total fat in grams (number)
- carbs: Carbohydrates in grams (number)
- fiber: Fiber in grams (number)
- sugar: Sugar in grams (number)
- sodium: Sodium in milligrams (number)
- cholesterol: Cholesterol in milligrams (number)
- saturated_fat: Saturated fat in grams (number)
- trans_fat: Trans fat in grams (number)
- vitamin_c: Vitamin C in milligrams (number)
- calcium: Calcium in milligrams (number)
- iron: Iron in milligrams (number)
- potassium: Potassium in milligrams (number)
- vitamin_d: Vitamin D in micrograms (number)
- serving_size: Description of serving size (string)
- items: Array of individual food items with name, calories, and portion

Be as accurate as possible with nutritional estimates based on typical values for the foods you identify."""

        payload = {
            "model": model_name,
            "messages": [
                {
                    "role": "user",
                    "content": nutrition_prompt
                }
            ],
            "images": [image_b64],
            "stream": False,
            "format": NUTRITION_SCHEMA
        }

        # Send request to Ollama
        response = requests.post(
            f"{OLLAMA_URL}/api/chat",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=300
        )

        response_time_ms = int((time.time() - start_time) * 1000)

        if not response.ok:
            error_msg = f"Ollama error: {response.text}"
            log_api_usage(
                endpoint='/ai/api/image/',
                method='POST',
                status_code=response.status_code,
                response_time_ms=response_time_ms,
                request_size=request_size,
                error_message=error_msg
            )

            return jsonify({
                'error': 'Analysis failed',
                'message': f'Model returned error: {response.text}'
            }), response.status_code

        # Parse response
        data = response.json()

        if 'message' not in data or 'content' not in data['message']:
            error_msg = "Unexpected response format from model"
            log_api_usage(
                endpoint='/ai/api/image/',
                method='POST',
                status_code=500,
                response_time_ms=response_time_ms,
                request_size=request_size,
                error_message=error_msg
            )

            return jsonify({
                'error': 'Invalid response',
                'message': 'Model returned unexpected response format'
            }), 500

        try:
            # Parse the structured nutrition data
            nutrition_info = json.loads(data['message']['content'])

            # Check for sample data and reject it
            food_name = nutrition_info.get('food_name') or nutrition_info.get('name')
            calories = nutrition_info.get('calories')

            if food_name == 'Grilled Salmon' and calories == 420:
                error_msg = "Sample data detected from Ollama model. Please try again with a clearer image."
                log_api_usage(
                    endpoint='/ai/api/image/',
                    method='POST',
                    status_code=400,
                    response_time_ms=response_time_ms,
                    request_size=request_size,
                    error_message=error_msg
                )
                return jsonify({
                    'error': 'Sample data detected',
                    'message': error_msg
                }), 400

            # Additional checks for other common sample data patterns
            sample_patterns = [
                {'name': 'Grilled Salmon', 'calories': 420},
                {'name': 'Sample Food', 'calories': 300},
                {'name': 'Test Food', 'calories': 250},
                {'name': 'Demo Food', 'calories': 200},
                {'name': 'Example Meal', 'calories': 350}
            ]

            for pattern in sample_patterns:
                if food_name == pattern['name'] and calories == pattern['calories']:
                    error_msg = f"Sample data detected from Ollama model ({pattern['name']}). Please try again with a clearer image."
                    log_api_usage(
                        endpoint='/ai/api/image/',
                        method='POST',
                        status_code=400,
                        response_time_ms=response_time_ms,
                        request_size=request_size,
                        error_message=error_msg
                    )
                    return jsonify({
                        'error': 'Sample data detected',
                        'message': error_msg
                    }), 400

            # Log successful usage
            log_api_usage(
                endpoint='/ai/api/image/',
                method='POST',
                status_code=200,
                response_time_ms=response_time_ms,
                request_size=request_size,
                response_size=len(json.dumps(nutrition_info))
            )

            return jsonify(nutrition_info)

        except json.JSONDecodeError as e:
            error_msg = f"Failed to parse nutrition data: {str(e)}"
            log_api_usage(
                endpoint='/ai/api/image/',
                method='POST',
                status_code=500,
                response_time_ms=response_time_ms,
                request_size=request_size,
                error_message=error_msg
            )

            return jsonify({
                'error': 'Parse error',
                'message': 'Failed to parse structured nutrition data',
                'raw_content': data['message']['content']
            }), 500

    except requests.RequestException as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = f"Ollama connection error: {str(e)}"

        log_api_usage(
            endpoint='/ai/api/image/',
            method='POST',
            status_code=503,
            response_time_ms=response_time_ms,
            request_size=len(request.data) if request.data else 0,
            error_message=error_msg
        )

        return jsonify({
            'error': 'Service unavailable',
            'message': 'Ollama service is not available'
        }), 503

    except Exception as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = f"Unexpected error: {str(e)}"

        log_api_usage(
            endpoint='/ai/api/image/',
            method='POST',
            status_code=500,
            response_time_ms=response_time_ms,
            request_size=len(request.data) if request.data else 0,
            error_message=error_msg
        )

        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred'
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8002, debug=os.getenv('DEBUG', 'false').lower() == 'true')
